* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0a0e27 100%);
    background-attachment: fixed;
    color: #ffffff;
    min-height: 100vh;
    overflow-x: auto;
    overflow-y: auto;
}

.dashboard-container {
    min-width: 1200px;
    max-width: 100%;
    padding: 20px;
    position: relative;
    height: auto;
}

/* 头部样式 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background: linear-gradient(90deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    margin-bottom: 20px;
    position: relative;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.dashboard-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #00ccff;
    text-shadow: 0 0 10px rgba(0, 204, 255, 0.5);
}

.date-time, .system-status {
    font-size: 14px;
    color: #88ccff;
}

/* 数据卡片样式 */
.data-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.data-card {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.data-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00aaff, #0088cc);
}

.data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 150, 255, 0.2);
}

.card-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-bg {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #00aaff, #0088cc);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 5px 15px rgba(0, 150, 255, 0.3);
}

.card-value {
    font-size: 28px;
    font-weight: 700;
    color: #00ccff;
    margin-bottom: 5px;
    text-shadow: 0 0 10px rgba(0, 204, 255, 0.3);
}

.card-label {
    font-size: 14px;
    color: #88ccff;
    opacity: 0.9;
}

/* 主要内容区域 */
.dashboard-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 图表容器样式 */
.chart-container {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 100, 200, 0.03) 100%);
    border: 1px solid rgba(0, 150, 255, 0.25);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    overflow: hidden;
    height: 400px;
    min-height: 400px;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.chart-container.small {
    height: 300px;
    min-height: 300px;
}

.chart-container.wide {
    width: 100%;
    height: 400px;
    min-height: 400px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    font-size: 18px;
    color: #00ccff;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 6px;
    color: #88ccff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 150, 255, 0.2);
    color: #00ccff;
}

.control-btn.active {
    background: linear-gradient(135deg, #00aaff, #0088cc);
    color: white;
    box-shadow: 0 3px 10px rgba(0, 150, 255, 0.3);
}

/* 数据表格样式 */
.data-table-container {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 100, 200, 0.03) 100%);
    border: 1px solid rgba(0, 150, 255, 0.25);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.data-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    font-size: 18px;
    color: #00ccff;
    font-weight: 600;
}

.table-controls {
    display: flex;
    gap: 10px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.2), rgba(0, 100, 200, 0.1));
    color: #00ccff;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.1);
    color: #cccccc;
}

.data-table tr:hover {
    background: rgba(0, 150, 255, 0.05);
}

/* 底部区域 */
.bottom-section {
    margin-bottom: 20px;
}

/* 底部导航 */
.dashboard-footer {
    background: linear-gradient(90deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    padding: 15px 30px;
    position: relative;
}

.dashboard-footer::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.footer-nav {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.nav-btn {
    padding: 10px 20px;
    background: transparent;
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 8px;
    color: #88ccff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 150, 255, 0.1);
    color: #00ccff;
}

.nav-btn.active {
    background: linear-gradient(135deg, #00aaff, #0088cc);
    color: white;
    box-shadow: 0 3px 10px rgba(0, 150, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .dashboard-main {
        grid-template-columns: 1fr;
    }
    
    .data-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .data-cards {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .footer-nav {
        flex-wrap: wrap;
        gap: 15px;
    }
}
