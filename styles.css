* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: linear-gradient(135deg, #000814 0%, #001d3d 30%, #003566 70%, #000814 100%);
    background-attachment: fixed;
    color: #ffffff;
    min-height: 100vh;
    overflow-x: auto;
    overflow-y: auto;
}

.dashboard-container {
    min-width: 1400px;
    max-width: 100%;
    padding: 0;
    position: relative;
    height: 100vh;
    background: radial-gradient(ellipse at center, rgba(0, 150, 255, 0.1) 0%, transparent 70%);
}

/* 科技边框装饰 */
.tech-border-top {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 3px;
    background: linear-gradient(90deg,
        transparent 0%,
        #00aaff 10%,
        #0088ff 50%,
        #00aaff 90%,
        transparent 100%);
    box-shadow: 0 0 10px #00aaff;
}

.tech-border-bottom {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    height: 3px;
    background: linear-gradient(90deg,
        transparent 0%,
        #00aaff 10%,
        #0088ff 50%,
        #00aaff 90%,
        transparent 100%);
    box-shadow: 0 0 10px #00aaff;
}

.tech-corner {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid #00aaff;
    box-shadow: 0 0 15px rgba(0, 170, 255, 0.6);
}

.tech-corner-tl {
    top: 20px;
    left: 20px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 10px;
}

.tech-corner-tr {
    top: 20px;
    right: 20px;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 10px;
}

.tech-corner-bl {
    bottom: 20px;
    left: 20px;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 10px;
}

.tech-corner-br {
    bottom: 20px;
    right: 20px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 10px;
}

/* 头部样式 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 40px 60px 20px 60px;
    position: relative;
    z-index: 10;
}

.header-left {
    flex: 1;
}

.header-center {
    flex: 2;
    text-align: center;
}

.header-right {
    flex: 1;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
}

.header-center h1 {
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
    letter-spacing: 1px;
    margin-top: 10px;
}

.date-time {
    font-size: 16px;
    color: #00aaff;
    text-shadow: 0 0 8px rgba(0, 170, 255, 0.6);
    font-weight: 500;
}

.system-status {
    font-size: 14px;
    color: #00aaff;
    text-shadow: 0 0 8px rgba(0, 170, 255, 0.6);
}

.status-icon {
    width: 8px;
    height: 8px;
    background: #00ff88;
    border-radius: 50%;
    box-shadow: 0 0 10px #00ff88;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 数据卡片样式 */
.data-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin: 0 60px 40px 60px;
    padding: 0;
}

.data-card {
    background: linear-gradient(135deg,
        rgba(0, 50, 100, 0.8) 0%,
        rgba(0, 30, 80, 0.9) 50%,
        rgba(0, 20, 60, 0.95) 100%);
    border: 2px solid #00aaff;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow:
        0 0 20px rgba(0, 170, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    height: 180px;
}

.data-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        #00aaff 20%,
        #00ccff 50%,
        #00aaff 80%,
        transparent 100%);
    box-shadow: 0 0 10px #00aaff;
}

.data-card:hover {
    transform: translateY(-3px);
    box-shadow:
        0 0 30px rgba(0, 170, 255, 0.5),
        0 10px 20px rgba(0, 170, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    position: relative;
}

.card-svg {
    width: 40px;
    height: 40px;
    fill: #00ccff;
    filter: drop-shadow(0 0 10px rgba(0, 204, 255, 0.6));
    position: relative;
    z-index: 2;
}

.card-icon::before {
    content: '';
    position: absolute;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg,
        rgba(0, 170, 255, 0.2) 0%,
        rgba(0, 120, 200, 0.3) 50%,
        rgba(0, 80, 150, 0.4) 100%);
    border-radius: 50%;
    box-shadow:
        0 0 20px rgba(0, 170, 255, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.1);
    transform: perspective(100px) rotateX(15deg);
}

.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.card-value {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
    text-shadow: 0 0 15px rgba(0, 204, 255, 0.8);
    letter-spacing: 0.5px;
}

.card-label {
    font-size: 13px;
    color: #88ccff;
    opacity: 0.9;
    font-weight: 500;
}

/* 主要内容区域 */
.dashboard-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 图表容器样式 */
.chart-container {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 100, 200, 0.03) 100%);
    border: 1px solid rgba(0, 150, 255, 0.25);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    overflow: hidden;
    height: 400px;
    min-height: 400px;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.chart-container.small {
    height: 300px;
    min-height: 300px;
}

.chart-container.wide {
    width: 100%;
    height: 400px;
    min-height: 400px;
}

/* 确保canvas不会导致容器变化 */
.chart-container canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    font-size: 18px;
    color: #00ccff;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 6px;
    color: #88ccff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 150, 255, 0.2);
    color: #00ccff;
}

.control-btn.active {
    background: linear-gradient(135deg, #00aaff, #0088cc);
    color: white;
    box-shadow: 0 3px 10px rgba(0, 150, 255, 0.3);
}

/* 数据表格样式 */
.data-table-container {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.08) 0%, rgba(0, 100, 200, 0.03) 100%);
    border: 1px solid rgba(0, 150, 255, 0.25);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.data-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    font-size: 18px;
    color: #00ccff;
    font-weight: 600;
}

.table-controls {
    display: flex;
    gap: 10px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.2), rgba(0, 100, 200, 0.1));
    color: #00ccff;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid rgba(0, 150, 255, 0.3);
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.1);
    color: #cccccc;
}

.data-table tr:hover {
    background: rgba(0, 150, 255, 0.05);
}

/* 底部区域 */
.bottom-section {
    margin-bottom: 20px;
}

/* 底部导航 */
.dashboard-footer {
    background: linear-gradient(90deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 10px;
    padding: 15px 30px;
    position: relative;
}

.dashboard-footer::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00aaff 50%, transparent 100%);
}

.footer-nav {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.nav-btn {
    padding: 10px 20px;
    background: transparent;
    border: 1px solid rgba(0, 150, 255, 0.3);
    border-radius: 8px;
    color: #88ccff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 150, 255, 0.1);
    color: #00ccff;
}

.nav-btn.active {
    background: linear-gradient(135deg, #00aaff, #0088cc);
    color: white;
    box-shadow: 0 3px 10px rgba(0, 150, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .dashboard-main {
        grid-template-columns: 1fr;
    }
    
    .data-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .data-cards {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .footer-nav {
        flex-wrap: wrap;
        gap: 15px;
    }
}
