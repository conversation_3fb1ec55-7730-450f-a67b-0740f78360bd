// 全局配置
const chartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        intersect: false,
        mode: 'index'
    },
    animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
    },
    plugins: {
        legend: {
            labels: {
                color: '#88ccff',
                font: {
                    size: 12
                }
            }
        }
    },
    scales: {
        x: {
            ticks: {
                color: '#88ccff',
                font: {
                    size: 11
                }
            },
            grid: {
                color: 'rgba(0, 150, 255, 0.1)'
            }
        },
        y: {
            ticks: {
                color: '#88ccff',
                font: {
                    size: 11
                }
            },
            grid: {
                color: 'rgba(0, 150, 255, 0.1)'
            }
        }
    }
};

// 初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    updateDateTime();
    setInterval(updateDateTime, 1000);
    animateDataCards();
});

// 更新时间显示
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };
    const dateTimeStr = now.toLocaleDateString('zh-CN', options);
    document.querySelector('.date-time').textContent = dateTimeStr;
}

// 数据卡片动画（只执行一次）
function animateDataCards() {
    const cards = document.querySelectorAll('.data-card');
    cards.forEach((card, index) => {
        // 检查是否已经动画过
        if (card.dataset.animated) return;

        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            card.dataset.animated = 'true'; // 标记已动画
        }, index * 200 + 100);
    });
}

// 初始化所有图表
function initializeCharts() {
    initMonthlyChart();
    initCategoryChart();
    initTrendChart();
    initBranchChart();
}

// 月度营业数据图表
function initMonthlyChart() {
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'],
            datasets: [{
                label: '本月收入',
                data: [12000, 15000, 13000, 16000, 14000, 18000, 17000, 19000, 16000, 20000, 18000, 22000],
                backgroundColor: 'rgba(0, 170, 255, 0.6)',
                borderColor: '#00aaff',
                borderWidth: 1,
                borderRadius: 4
            }, {
                label: '上月收入',
                data: [10000, 12000, 11000, 14000, 12000, 15000, 14000, 16000, 13000, 17000, 15000, 19000],
                backgroundColor: 'rgba(0, 200, 255, 0.3)',
                borderColor: '#00c8ff',
                borderWidth: 1,
                borderRadius: 4
            }]
        },
        options: {
            ...chartConfig,
            plugins: {
                ...chartConfig.plugins,
                title: {
                    display: false
                }
            }
        }
    });
}

// 分类营业数据图表
function initCategoryChart() {
    const ctx = document.getElementById('categoryChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            datasets: [{
                label: '收费收入',
                data: [8000, 9500, 8500, 10000, 9000, 11000, 10500, 12000, 10000, 13000, 11500, 14000],
                borderColor: '#00aaff',
                backgroundColor: 'rgba(0, 170, 255, 0.1)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#00aaff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            }, {
                label: '其他收入',
                data: [4000, 5500, 4500, 6000, 5000, 7000, 6500, 7000, 6000, 7000, 6500, 8000],
                borderColor: '#00c8ff',
                backgroundColor: 'rgba(0, 200, 255, 0.1)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#00c8ff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: chartConfig
    });
}

// 趋势图表
function initTrendChart() {
    const ctx = document.getElementById('trendChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '本周',
                data: [15000, 18000, 16000, 19000, 17000, 21000, 19000],
                borderColor: '#00aaff',
                backgroundColor: 'rgba(0, 170, 255, 0.2)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#00aaff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5
            }, {
                label: '上周',
                data: [13000, 16000, 14000, 17000, 15000, 19000, 17000],
                borderColor: '#ff6b6b',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#ff6b6b',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: chartConfig
    });
}

// 分公司数据图表
function initBranchChart() {
    const ctx = document.getElementById('branchChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['分公司一', '分公司二', '分公司三', '分公司四', '分公司五', '分公司六', '分公司七', '分公司八', '分公司九', '分公司十', '分公司十一', '分公司十二', '分公司十三', '分公司十四', '分公司十五', '分公司十六'],
            datasets: [{
                label: '营业收入',
                data: [15000, 18000, 16000, 19000, 17000, 21000, 19000, 16000, 18000, 17000, 19000, 16000, 18000, 17000, 19000, 16000],
                backgroundColor: 'rgba(0, 170, 255, 0.6)',
                borderColor: '#00aaff',
                borderWidth: 1,
                borderRadius: 4
            }, {
                label: '实收金额',
                data: [14000, 17000, 15000, 18000, 16000, 20000, 18000, 15000, 17000, 16000, 18000, 15000, 17000, 16000, 18000, 15000],
                backgroundColor: 'rgba(0, 200, 255, 0.4)',
                borderColor: '#00c8ff',
                borderWidth: 1,
                borderRadius: 4
            }, {
                label: '完成率',
                data: [93, 94, 94, 95, 94, 95, 95, 94, 94, 94, 95, 94, 94, 94, 95, 94],
                type: 'line',
                borderColor: '#ffd700',
                backgroundColor: 'rgba(255, 215, 0, 0.1)',
                yAxisID: 'y1',
                tension: 0.4,
                pointBackgroundColor: '#ffd700',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            ...chartConfig,
            scales: {
                ...chartConfig.scales,
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    ticks: {
                        color: '#88ccff',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        drawOnChartArea: false,
                        color: 'rgba(0, 150, 255, 0.1)'
                    }
                }
            }
        }
    });
}

// 按钮交互
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('control-btn')) {
        // 移除同组按钮的active状态
        const parent = e.target.parentElement;
        parent.querySelectorAll('.control-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        // 添加当前按钮的active状态
        e.target.classList.add('active');
    }
    
    if (e.target.classList.contains('nav-btn')) {
        // 移除所有导航按钮的active状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        // 添加当前按钮的active状态
        e.target.classList.add('active');
    }
});

// 数据更新函数（模拟实时数据）
function updateData() {
    // 这里可以添加实时数据更新逻辑
    // 例如通过API获取新数据并更新图表
}

// 每30秒更新一次数据
setInterval(updateData, 30000);
